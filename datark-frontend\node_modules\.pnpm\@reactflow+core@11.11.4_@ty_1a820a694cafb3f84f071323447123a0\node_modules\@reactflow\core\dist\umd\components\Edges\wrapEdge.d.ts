import React from 'react';
import type { ComponentType } from 'react';
import type { EdgeProps, WrapEdgeProps } from '../../types';
declare const _default: (EdgeComponent: ComponentType<EdgeProps>) => React.MemoExoticComponent<{
    ({ id, className, type, data, onClick, onEdgeDoubleClick, selected, animated, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, source, target, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, elementsSelectable, hidden, sourceHandleId, targetHandleId, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, markerEnd, markerStart, rfId, ariaLabel, isFocusable, isReconnectable, pathOptions, interactionWidth, disableKeyboardA11y, }: WrapEdgeProps): JSX.Element | null;
    displayName: string;
}>;
export default _default;
//# sourceMappingURL=wrapEdge.d.ts.map