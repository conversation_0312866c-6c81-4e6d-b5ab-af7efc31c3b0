import React from 'react';
import type { MiniMapProps } from './types';
declare function MiniMap({ style, className, nodeStrokeColor, nodeColor, nodeClassName, nodeBorderRadius, nodeStrokeWidth, nodeComponent, maskColor, maskStrokeColor, maskStrokeWidth, position, onClick, onNodeClick, pannable, zoomable, ariaLabel, inversePan, zoomStep, offsetScale, }: MiniMapProps): JSX.Element;
declare namespace MiniMap {
    var displayName: string;
}
declare const _default: React.MemoExoticComponent<typeof MiniMap>;
export default _default;
//# sourceMappingURL=MiniMap.d.ts.map