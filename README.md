# DataRK - 无代码数据生成平台

一款面向非技术人员的无代码Web应用，用户通过拖拽式图形界面定义数据模型与关系，并一键调用DeepSeek AI生成高度仿真的模拟数据。

## 项目结构

```
datark/
├── datark-frontend/          # React + TypeScript 前端
│   ├── src/
│   ├── package.json
│   └── ...
├── datark-backend/           # FastAPI Python 后端
│   ├── app/
│   ├── requirements.txt
│   └── ...
└── README.md
```

## 技术栈

### 前端
- React 19
- TypeScript
- TailwindCSS
- Shadcn/ui
- ReactFlow (可视化编辑器)
- Monaco Editor
- Axios

### 后端
- FastAPI
- SQLAlchemy
- PostgreSQL
- Uvicorn
- DeepSeek API

## 快速开始

### 前端开发

```bash
cd datark-frontend
pnpm install
pnpm run dev
```

### 后端开发

```bash
cd datark-backend
pip install -r requirements.txt
cp .env.example .env
# 编辑 .env 文件配置数据库和API密钥
python main.py
```

## 核心功能

1. **工作流管理** - 创建、编辑、执行数据生成工作流
2. **可视化编辑器** - 拖拽式数据表和关系定义
3. **AI数据生成** - 基于DeepSeek的智能数据生成
4. **数据导出** - 支持JSON、CSV、SQL格式导出

## 目标用户

- 产品经理/业务分析师
- 软件测试工程师  
- 学生与教育者

## 开发状态

🚧 项目正在开发中...
