{"name": "@reactflow/node-toolbar", "version": "1.3.14", "description": "A toolbar component for React Flow that can be attached to a node.", "keywords": ["react", "node-based UI", "graph", "diagram", "workflow", "react-flow"], "files": ["dist"], "source": "src/index.tsx", "main": "dist/umd/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.mjs", "require": "./dist/umd/index.js"}}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xyflow/xyflow.git", "directory": "packages/node-toolbar"}, "dependencies": {"classcat": "^5.0.3", "zustand": "^4.4.1", "@reactflow/core": "11.11.4"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "devDependencies": {"@types/node": "^18.7.16", "@types/react": ">=17", "@types/react-dom": ">=17", "react": "^18.2.0", "typescript": "^4.9.4", "@reactflow/tsconfig": "0.0.0", "@reactflow/eslint-config": "0.0.0", "@reactflow/rollup-config": "0.0.0"}, "rollup": {"globals": {"zustand": "Zustand", "zustand/shallow": "zustandShallow", "classcat": "cc"}, "name": "ReactFlowNodeToolbar"}, "scripts": {"dev": "concurrently \"rollup --config node:@reactflow/rollup-config --watch\" pnpm:css-watch", "build": "rollup --config node:@reactflow/rollup-config --environment NODE_ENV:production", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "typecheck": "tsc --noEmit"}}