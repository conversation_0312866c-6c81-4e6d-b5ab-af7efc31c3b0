import { Edge, HandleElement } from './types';
export declare const errorMessages: {
    error001: () => string;
    error002: () => string;
    error003: (nodeType: string) => string;
    error004: () => string;
    error005: () => string;
    error006: () => string;
    error007: (id: string) => string;
    error009: (type: string) => string;
    error008: (sourceHandle: HandleElement | null, edge: Edge) => string;
    error010: () => string;
    error011: (edgeType: string) => string;
    error012: (id: string) => string;
};
//# sourceMappingURL=contants.d.ts.map