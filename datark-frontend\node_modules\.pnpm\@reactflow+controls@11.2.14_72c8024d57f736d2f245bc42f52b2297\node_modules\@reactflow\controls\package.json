{"name": "@reactflow/controls", "version": "11.2.14", "description": "Component to control the viewport of a React Flow instance", "keywords": ["react", "node-based UI", "graph", "diagram", "workflow", "react-flow"], "files": ["dist"], "source": "src/index.tsx", "main": "dist/umd/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.mjs", "require": "./dist/umd/index.js"}, "./dist/style.css": "./dist/style.css"}, "sideEffects": ["*.css"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xyflow/xyflow.git", "directory": "packages/controls"}, "publishConfig": {"access": "public"}, "dependencies": {"classcat": "^5.0.3", "zustand": "^4.4.1", "@reactflow/core": "11.11.4"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "devDependencies": {"@types/node": "^18.7.16", "@types/react": ">=17", "typescript": "^4.9.4", "@reactflow/eslint-config": "0.0.0", "@reactflow/rollup-config": "0.0.0", "@reactflow/tsconfig": "0.0.0"}, "rollup": {"globals": {"classcat": "cc", "zustand": "Zustand", "zustand/shallow": "zustandShallow"}, "name": "ReactFlowControls"}, "scripts": {"dev": "concurrently \"rollup --config node:@reactflow/rollup-config --watch\" pnpm:css-watch", "build": "rollup --config node:@reactflow/rollup-config --environment NODE_ENV:production && npm run css", "css": "postcss src/*.css --config ../../tooling/postcss-config/postcss.config.js --dir dist", "css-watch": "pnpm css --watch", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "typecheck": "tsc --noEmit"}}