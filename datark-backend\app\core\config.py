"""
Application Configuration
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Database Configuration
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    
    # DeepSeek API Configuration
    DEEPSEEK_API_KEY: str = Field(..., env="DEEPSEEK_API_KEY")
    DEEPSEEK_API_URL: str = Field(
        default="https://api.deepseek.com/v1", 
        env="DEEPSEEK_API_URL"
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
