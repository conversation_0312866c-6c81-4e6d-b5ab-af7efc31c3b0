import React, { HTMLAttributes } from 'react';
import { type HandleProps } from '../../types';
export type HandleComponentProps = HandleProps & Omit<HTMLAttributes<HTMLDivElement>, 'id'>;
declare const _default: React.MemoExoticComponent<React.ForwardRefExoticComponent<HandleProps & Omit<React.HTMLAttributes<HTMLDivElement>, "id"> & React.RefAttributes<HTMLDivElement>>>;
export default _default;
//# sourceMappingURL=index.d.ts.map