{"version": 3, "file": "nodes.d.ts", "sourceRoot": "", "sources": ["../../../../packages/core/src/types/nodes.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,aAAa,EAAE,UAAU,IAAI,eAAe,EAAE,MAAM,OAAO,CAAC;AAE1E,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAC3C,OAAO,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,GAAG,CAAC;AAG/E,MAAM,MAAM,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,MAAM,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,IAAI;IAC7E,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,EAAE,UAAU,CAAC;IACrB,IAAI,EAAE,CAAC,CAAC;IACR,IAAI,CAAC,EAAE,CAAC,CAAC;IACT,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,cAAc,CAAC,EAAE,QAAQ,CAAC;IAC1B,cAAc,CAAC,EAAE,QAAQ,CAAC;IAC1B,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACvB,yCAAyC;IACzC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,QAAQ,GAAG,gBAAgB,CAAC;IACrC,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,gBAAgB,CAAC,EAAE,UAAU,CAAC;IAC9B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,QAAQ,CAAC,EAAE,OAAO,CAAC;IAGnB,CAAC,eAAe,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,EAAE,MAAM,CAAC;QACX,YAAY,CAAC,EAAE,gBAAgB,CAAC;QAChC,QAAQ,CAAC,EAAE,OAAO,CAAC;KACpB,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;AAC5E,MAAM,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;AAC1F,MAAM,MAAM,oBAAoB,GAAG,CAAC,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;AAEnF,MAAM,MAAM,aAAa,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CACvC,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,GAAG,MAAM,GAAG,OAAO,GAAG,WAAW,GAAG,YAAY,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,QAAQ,GAAG,WAAW,CACpH,GACC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC,GAAG;IACxD,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,OAAO,CAAC;IACrB,YAAY,EAAE,OAAO,CAAC;IACtB,WAAW,EAAE,OAAO,CAAC;IACrB,WAAW,EAAE,OAAO,CAAC;IACrB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,OAAO,CAAC,EAAE,gBAAgB,CAAC;IAC3B,aAAa,CAAC,EAAE,gBAAgB,CAAC;IACjC,YAAY,CAAC,EAAE,gBAAgB,CAAC;IAChC,WAAW,CAAC,EAAE,gBAAgB,CAAC;IAC/B,YAAY,CAAC,EAAE,gBAAgB,CAAC;IAChC,aAAa,CAAC,EAAE,gBAAgB,CAAC;IACjC,cAAc,EAAE,cAAc,GAAG,IAAI,CAAC;IACtC,QAAQ,EAAE,OAAO,CAAC;IAClB,eAAe,EAAE,MAAM,CAAC;IACxB,cAAc,EAAE,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,mBAAmB,EAAE,OAAO,CAAC;IAC7B,eAAe,EAAE,OAAO,CAAC;CAC1B,CAAC;AAGJ,MAAM,MAAM,SAAS,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CACnC,aAAa,CAAC,CAAC,CAAC,EAChB,IAAI,GAAG,MAAM,GAAG,YAAY,GAAG,MAAM,GAAG,UAAU,GAAG,eAAe,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,CAClG,GAAG;IACF,QAAQ,EAAE,OAAO,CAAC;IAClB,cAAc,CAAC,EAAE,QAAQ,CAAC;IAC1B,cAAc,CAAC,EAAE,QAAQ,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;IAC/B,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;CAChC,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,WAAW,EAAE,cAAc,CAAC;IAC5B,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAE9C,MAAM,MAAM,UAAU,GAAG,UAAU,GAAG;IACpC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,EAAE,UAAU,CAAC;IACrB,gBAAgB,EAAE,UAAU,CAAC;IAE7B,QAAQ,EAAE,UAAU,CAAC;IACrB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACvB,MAAM,CAAC,EAAE,QAAQ,GAAG,gBAAgB,CAAC;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,YAAY,CAAC,EAAE,OAAO,CAAC;CACxB,CAAC;AAEF,MAAM,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC"}